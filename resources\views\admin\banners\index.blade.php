@extends('layouts.admin')

@section('title', 'จัดการแบนเนอร์')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">จัดการแบนเนอร์</h3>
                    <a href="{{ route('admin.banners.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> เพิ่มแบนเนอร์ใหม่
                    </a>
                </div>

                <div class="card-body">

                    @if($banners->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>รูปภาพ</th>
                                        <th>ชื่อแบนเนอร์</th>
                                        <th>คำอธิบาย</th>
                                        <th>แสดงในหน้า</th>
                                        <th>ลำดับ</th>
                                        <th>สถานะ</th>
                                        <th>วันที่สร้าง</th>
                                        <th>จัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($banners as $banner)
                                    <tr>
                                        <td>
                                            @if($banner->image_path)
                                                <img src="{{ asset('storage/' . $banner->image_path) }}" 
                                                     alt="{{ $banner->title }}" 
                                                     class="img-thumbnail" 
                                                     style="width: 80px; height: 50px; object-fit: cover;">
                                            @else
                                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                                     style="width: 80px; height: 50px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            @endif
                                        </td>
                                        <td>{{ $banner->title }}</td>
                                        <td>{{ Str::limit($banner->description, 50) }}</td>
                                        <td>
                                            <span class="badge bg-secondary">{{ $banner->display_pages_name }}</span>
                                        </td>
                                        <td>{{ $banner->sort_order }}</td>
                                        <td>
                                            @if($banner->is_active)
                                                <span class="badge bg-success">เปิดใช้งาน</span>
                                            @else
                                                <span class="badge bg-secondary">ปิดใช้งาน</span>
                                            @endif
                                        </td>
                                        <td>{{ $banner->created_at->format('d/m/Y H:i') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.banners.edit', $banner) }}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('admin.banners.destroy', $banner) }}"
                                                      method="POST"
                                                      class="d-inline"
                                                      id="deleteBannerForm{{ $banner->id }}">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="handleDeleteBanner({{ $banner->id }})">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $banners->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">ยังไม่มีแบนเนอร์</h5>
                            <p class="text-muted">เริ่มต้นโดยการเพิ่มแบนเนอร์แรกของคุณ</p>
                            <a href="{{ route('admin.banners.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> เพิ่มแบนเนอร์ใหม่
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@section('scripts')
<script>
// Delete banner function with custom modal
async function handleDeleteBanner(bannerId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบแบนเนอร์นี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบแบนเนอร์'
    );

    if (confirmed) {
        document.getElementById(`deleteBannerForm${bannerId}`).submit();
    }
}
</script>
@endsection
