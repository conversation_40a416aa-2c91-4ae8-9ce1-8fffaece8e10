@extends('layouts.admin')

@section('title', 'จัดการแบนเนอร์ - Phuyai Prajak Service Shop Admin')

@section('content')
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                    <i class="fas fa-images me-2"></i>จัดการแบนเนอร์
                </h1>
                <p class="text-muted mb-0">จัดการแบนเนอร์ทั้งหมดของคุณ</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.banners.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มแบนเนอร์ใหม่
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-4">
        <div class="card stats-card h-100">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">แบนเนอร์ทั้งหมด</h6>
                        <h3 class="mb-0">{{ $totalBanners }}</h3>
                    </div>
                    <i class="fas fa-images fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100" style="background: linear-gradient(135deg, var(--success-color), #059669);">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">แบนเนอร์ที่เปิดใช้</h6>
                        <h3 class="mb-0">{{ $activeBanners }}</h3>
                    </div>
                    <i class="fas fa-toggle-on fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100" style="background: linear-gradient(135deg, var(--warning-color), #f39c12);">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">แบนเนอร์หน้าหลัก</h6>
                        <h3 class="mb-0">{{ $homeBanners }}</h3>
                    </div>
                    <i class="fas fa-home fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Banners Content -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>รายการแบนเนอร์
            </h5>
        </div>
    </div>
    <div class="card-body">
        @if($banners->count() > 0)
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>รูปภาพ</th>
                            <th>ชื่อแบนเนอร์</th>
                            <th>คำอธิบาย</th>
                            <th>แสดงในหน้า</th>
                            <th>ลำดับ</th>
                            <th>สถานะ</th>
                            <th>วันที่สร้าง</th>
                            <th>จัดการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($banners as $banner)
                        <tr>
                            <td>
                                @if($banner->image_path)
                                    <img src="{{ asset('storage/' . $banner->image_path) }}"
                                         alt="{{ $banner->title }}"
                                         class="img-thumbnail"
                                         style="width: 80px; height: 50px; object-fit: cover;">
                                @else
                                    <div class="bg-light d-flex align-items-center justify-content-center"
                                         style="width: 80px; height: 50px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                @endif
                            </td>
                            <td>{{ $banner->title }}</td>
                            <td>{{ Str::limit($banner->description, 50) }}</td>
                            <td>
                                <span class="badge bg-secondary">{{ $banner->display_pages_name }}</span>
                            </td>
                            <td>{{ $banner->sort_order }}</td>
                            <td>
                                @if($banner->is_active)
                                    <span class="badge bg-success">เปิดใช้งาน</span>
                                @else
                                    <span class="badge bg-secondary">ปิดใช้งาน</span>
                                @endif
                            </td>
                            <td>{{ $banner->created_at->format('d/m/Y H:i') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.banners.edit', $banner) }}"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.banners.destroy', $banner) }}"
                                          method="POST"
                                          class="d-inline"
                                          id="deleteBannerForm{{ $banner->id }}">
                                        @csrf
                                        @method('DELETE')
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="handleDeleteBanner({{ $banner->id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                {{ $banners->links() }}
            </div>
        @else
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-4"></i>
            <h4 class="text-muted mb-2">ยังไม่มีแบนเนอร์</h4>
            <p class="text-muted mb-4">เริ่มต้นด้วยการเพิ่มแบนเนอร์แรกของคุณ</p>
            <a href="{{ route('admin.banners.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>เพิ่มแบนเนอร์ใหม่
            </a>
        </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
// Delete banner function with custom modal
async function handleDeleteBanner(bannerId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบแบนเนอร์นี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบแบนเนอร์'
    );

    if (confirmed) {
        document.getElementById(`deleteBannerForm${bannerId}`).submit();
    }
}
</script>
@endsection
